<template>
  <div class="zgsTableManage-class">
    <div class="head-class">
      <el-row :gutter="12">
        <el-col :span="4">
          <el-input v-model="param.answerAndQuestion" class="inputWidth-class" @keyup.enter.native="search" placeholder="请输入问题/回复关键字"></el-input>
        </el-col>
        <el-col :span="4">
          <el-select-multiple
              v-model="param.feedbackTypeList"
              style="width: 100%"
              placeholder="反馈类型"
              ref="feedbackTypeList"
              clearable
              confirm>
            <el-select-multiple-option
                v-for="(item,index) in feedbackTypeOptions"
                :key="index"
                :label="item.label"
                :value="item.value">
            </el-select-multiple-option>
          </el-select-multiple>
        </el-col>
        <el-col :span="4">
          <el-input v-model="param.replyName" class="inputWidth-class" @keyup.enter.native="search" placeholder="回复人"></el-input>
        </el-col>
        <el-col :span="4">
          <el-input v-model="param.feedbackName" class="inputWidth-class" @keyup.enter.native="search" placeholder="用户名称"></el-input>
        </el-col>
        <el-col :span="8">
          <el-date-picker
              class="inputWidths-class"
              v-model="replyTime"
              type="datetimerange"
              :picker-options="pickerOptions"
              start-placeholder="首次反馈时间"
              end-placeholder="首次反馈时间"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handleDateChange">
          </el-date-picker>
        </el-col>
      </el-row>
    </div>
    <el-row :gutter="12" style="margin-bottom: 10px;">
      <el-col :span="12">
        <div style="font-size: 12px;"><span style="color: #606266">共</span>&nbsp;{{tableTotal}}&nbsp;<span style="color: #606266">条</span></div>
      </el-col>
      <el-col :span="12" class="button-group-col">
        <div class="button-group">
          <el-button type="primary" class="query-btn" @click="dosearch()">查询</el-button>
          <el-button type="primary" plain="reset-btn" @click="clearForm">重置</el-button>
          <el-button type="primary" plain @click="exportFieldList">导出</el-button>
        </div>
      </el-col>
    </el-row>
    <!--  数据表格-->
    <div class="body-class">
      <el-table :data="tableData" ref="multipleTable" stripe style="width: 100%">
        <el-table-column
            label="序号"
            align="center"
            type="index"
            width="60"
            fixed="left">
        </el-table-column>
        <el-table-column align="left" prop="" label="问题&答复" width="300" fixed="left">
          <template #default="scope">
            <div
                class="question-answer-container"
                :title="`问: ${scope.row.question || '--'}\n答: ${scope.row.answer || '--'}`"
            >
              <p class="question">
                <span style="color: #606266">问:</span> {{ scope.row.question ? scope.row.question : '--' }}
              </p>
              <p class="answer">
                <span style="color: #606266">答:</span> {{ scope.row.answer ? scope.row.answer : '--' }}
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="feedbackContent" label="反馈内容" width='300' fixed="left">
          <template scope="scope">
          <span :title="scope.row.feedbackContent"
                :style="{display: '-webkit-box',
            '-webkit-box-orient': 'vertical',
            '-webkit-line-clamp': '1',}">
            {{ scope.row.feedbackContent ? scope.row.feedbackContent : '--' }}
          </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="feedbackType" label="反馈类型" width='100' fixed="left">
          <template scope="scope">
            {{ feedbackTypeFormatter(scope.row.feedbackType) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="首次反馈时间" width="160" >
          <template scope="scope">
            <span v-if="scope.row.createTime">
              <p>{{scope.row.createTime.split(' ')[0]}}</p>
              <p>{{scope.row.createTime.split(' ')[1]}}</p>
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="userName" label="用户ID" width="160" >
          <template scope="scope">
            <span>{{ scope.row.userName ? scope.row.userName : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="feedbackName" label="用户姓名" width="160" >
          <template scope="scope">
            <span>{{ scope.row.feedbackName ? scope.row.feedbackName : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="telephone" label="手机号码" width="160" >
          <template scope="scope">
            <span>{{ scope.row.telephone ? scope.row.telephone : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="isReply" label="是否答复" width="100" >
          <template scope="scope">
            <span>
              {{ scope.row.isReply ? scope.row.isReply === '0' ? '否' : '是' : '--' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="replyName" label="答复人" width="160" >
          <template scope="scope">
            <span>
              {{ scope.row.replyName ? scope.row.replyName : '--' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="newReplyTime" label="最新答复时间" width="160" >
          <template scope="scope">
            <span v-if="scope.row.newReplyTime">
              <p>{{scope.row.newReplyTime.split(' ')[0]}}</p>
              <p>{{scope.row.newReplyTime.split(' ')[1]}}</p>
            </span>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="180" fixed="right" >
          <template scope="scope">
            <span v-if="scope.row.isFeedbackNew === '1'">
              <div class="source-marker">1</div>
            </span>
            <span v-if="scope.row.isReply === '0'">
              <el-link type="primary" :underline="false" @click="reply(scope.row)">答复</el-link>
            </span>
            <el-link type="primary" :underline="false" @click="replyHistory(scope.row)">历史答复</el-link>
            <el-link type="primary" :underline="false" @click="deleteRow(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
      <el-row>
        <el-pagination
            style="display: inline-block; margin-top: 10px;float: right"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10, 20, 50, 200]"
            :page-size="param.pageSize"
            :current-page="currentPage"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableTotal">
        </el-pagination>
      </el-row>
    </div>

    <!-- 答复详情对话框 -->
    <reply-detail-dialog
      v-if="replyDetailDialogVisible"
      :visible.sync="replyDetailDialogVisible"
      :feedback-data="selectedFeedbackData"
      :dialog-type="replyDetailDialogType"
      @close="handleReplyDetailDialogClose"
      @submit-success="handleReplySubmitSuccess"
    />
  </div>
</template>
<script>
import {_exportFieldList, _getDropDown, _getTableList} from "@/api/feedbackReply-api";
import {_deleteFeedback} from "@/api/chat";
import ReplyDetailDialog from "@/views/compliance/wenwen/replyDetailDialog.vue";

export default {
  name: 'feedbackReply',
  components: {
    ReplyDetailDialog
  },
  data() {
    return {
      param: {
        pageSize: 10,
        startRow: 0,
        answerAndQuestion: "",
        feedbackTypeList: [],
        replyName: "",
        feedbackName: "",
        startTime: "",
        endTime: ""
      },
      currentPage: 1,
      tableTotal: 10,
      feedbackTypeOptions: [{
        value: '1',
        label: '回答有误'
      }, {
        value: '2',
        label: '响应慢'
      }, {
        value: '3',
        label: '案例有误'
      }, {
        value: '4',
        label: '法规有误'
      }, {
        value: '0',
        label: '其它'
      }],
      pickerOptions: {
        shortcuts: [{
          text: '今天',
          onClick (picker) {
            const start = new Date();
            start.setHours(0, 0, 0, 0); // 设置为当天0点
            const end = new Date();
            end.setHours(23, 59, 59, 999); // 设置为当天23:59:59
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      tableData: [],
      replyTime:[],
      // 答复详情页面相关数据
      replyDetailDialogVisible: false,
      selectedFeedbackData: {},
      replyDetailDialogType: 'history' // 'history' 或 'reply'
    }
  },
  created() {
    this.getDropDown()
    this.search()
  },
  mounted() {
  },
  methods: {
    dosearch(){
      this.param.startRow = 0
      this.currentPage = 1
      this.search()
    },
    search() {
      _getTableList(this.param).then(res => {
        this.tableData = res.data.result.tableList
        this.tableTotal = res.data.result.totalSize
        if (res.data.result.tableList.length === 0) {
          this.param.startRow = 0
          this.currentPage = 1
          _getTableList(this.param).then(res => {
            this.tableData = res.data.result.tableList
            this.tableTotal = res.data.result.totalSize
          })
        }
      })
    },
    getDropDown() {
      // _getDropDown({}).then(res => {
        // this.feedbackTypeList = res.data.result;
      // })
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.param.startRow = (val - 1) * this.param.pageSize;
      this.search()
    },
    handleSizeChange(val) {
      this.param.pageSize = val;
      this.search()
    },
    handleDateChange(val) {
      if (val && val.length === 2) {
        this.param.startTime = val[0]; // 开始时间
        this.param.endTime = val[1];   // 结束时间
      } else {
        this.param.startTime = "";
        this.param.endTime = "";
      }
    },
    clearForm() {
      this.param = {
        pageSize: 10,
        startRow: 0,
        answerAndQuestion: "",
        feedbackTypeList: [],
        replyName: "",
        feedbackName: "",
        startTime: "",
        endTime: ""
      }
      this.$refs.feedbackTypeList.clear();
      this.replyTime=[]
      this.currentPage = 1
      this.dosearch()
    },
    exportFieldList() {
        if (this.tableData && this.tableData.length > 0) {
          _exportFieldList(this.param)
        } else {
          this.$message.warning('当前表格没有数据可导出');
          return;
      }

    },
    feedbackTypeFormatter (type) {
      const option = this.feedbackTypeOptions.find(item => item.value === type);
      return option ? option.label : '--';
    },
    reply (row) {
      // 打开答复详情页面，显示回复框
      console.log('点击答复按钮，反馈数据：', row);
      this.selectedFeedbackData = row;
      this.replyDetailDialogType = 'reply';
      this.replyDetailDialogVisible = true;
    },
    replyHistory (row) {
      // 打开答复详情页面，查看历史答复
      this.selectedFeedbackData = row;
      this.replyDetailDialogType = 'history';
      this.replyDetailDialogVisible = true;
    },
    deleteRow (row) {
      this.$confirm('删除后不可恢复，请确认是否删除。', '删除所选反馈', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        _deleteFeedback(row.chatContentId).then(res => {
          if (res.data.result) {
            this.$message({
              type: 'success',
              message: '删除成功'
            });
          } else {
            this.$message({
              type: 'warning',
              message: '删除失败'
            });
          }
        })
      })
    },
    handleReplyDetailDialogClose() {
      this.replyDetailDialogVisible = false;
      this.selectedFeedbackData = {};
    },
    handleReplySubmitSuccess() {
      // 处理答复提交成功后的逻辑
      console.log('答复提交成功');
      this.replyDetailDialogVisible = false;
      this.selectedFeedbackData = {};
      // 刷新列表数据
      this.search();
    }
  },
}
</script>
<style scoped lang="scss">
.zgsTableManage-class {
  padding: 10px 50px;
  .head-class {
    height: 120px;
  }
  .el-date-editor .el-range-input .large{
    background-color: #F6F7F9 !important
  }

  .inputWidth-class {
    width: 242px;

  }

  .button-group-col {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .inputWidths-class{
    width: 350px;

  }
  ::v-deep .el-date-editor .el-range-input.large{
    background-color: #F6F7F9 !important
  }
  .el-table__body-wrapper {
    max-width: 600px; /* 按需设置合适的最大宽度 */
  }
  .button-group{
    display: flex;
    gap: 8px;
  }

  .el-select .el-input__inner {
    width: 100%;
    background-color: #F6F7F9; /* 浅灰色背景 */
    border: 1px solid #e0e0e0; /* 浅灰色边框 */
    color: #333;
  }

  /deep/ .el-input__inner {
    background-color: #F6F7F9 !important; /* 浅灰色背景 */
    border: none; /* 浅灰色边框 */
    color: #333 !important;
  }

  /deep/ .el-table__fixed-arrow {
    display: none !important;
  }
  .el-button {
    border-radius: 20px !important; // 图片中的圆角半径
    padding: 8px 20px;
  }
  .flex-container {
    display: flex;
    justify-content: space-between;
    justify-content: flex-end;
    align-items: center;
    box-sizing: border-box;
    }
  ::v-deep .el-link--inner {
    border-bottom: none !important;
  }
  .question-answer-container {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
    white-space: normal;
    font-size: 14px; /* 根据实际情况调整 */
    line-height: 1.5; /* 控制行高，便于计算 */
    cursor: default;
  }

  .question {
    margin: 0;
    /* 限制问题最多显示1行，超出省略 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .answer {
    margin: 0;
    /* 限制回答最多显示2行 */
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
    white-space: normal;
    line-height: 1.5;
  }
  .source-marker {
    color: white;
    font-size: 12px;
    position: absolute;
    width: 14px;
    height: 12px;
    top: 18px;
    left: 55px;
    line-height: 1;
    background-color: red;
  }

  .source-marker::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    width: 0;
    height: 0;
    border-top: 6px solid red;
    border-right: 6px solid transparent;
  }
  .el-link.el-link--primary {
    border-color: #1990FE;
    color: #1990FE;
  }
}
</style>