<!--
  *@name replyDetailDialog.vue
  *<AUTHOR>
  *@date 2025/7/30
  *@description 答复详情页面组件
-->
<template>
  <el-dialog
    title="答复详情"
    :visible.sync="visible"
    width="1000px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :append-to-body="true"
    @close="handleClose"
    class="reply-detail-dialog"
    :padding="[0,0]"
  >
    <!-- 头部问答内容 -->
    <div class="header-qa-content">
      <div class="qa-item">
        <div class="qa-label">问:</div>
        <div class="qa-content">{{ feedbackData.question || '--' }}</div>
      </div>
      <div class="qa-item">
        <div class="qa-label">回:</div>
        <div class="qa-content">{{ feedbackData.answer || '--' }}</div>
      </div>
    </div>

    <!-- 聊天对话区域 -->
    <div class="chat-container" ref="chatContainer">
      <!-- Loading状态 -->
      <div v-if="chatLoading" class="chat-loading">
        <i class="el-icon-loading"></i>
        <span>加载对话记录中...</span>
      </div>

      <!-- 对话内容 -->
      <div v-else class="chat-messages">
        <div
          v-for="(message, index) in conversationList"
          :key="index"
          :class="['message-item', message.replyFeedbackStatus === '1' ? 'feedback-message' : 'reply-message']"
        >
          <!-- 反馈消息（用户发送，左侧） -->
          <div v-if="message.replyFeedbackStatus === '1'" class="message-left">
            <div class="avatar">{{ getAvatarText(message.realName, '1') }}</div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-time">{{ formatDateTime(message.createTime) }}</span>
              </div>
              <div class="message-text">{{ message.content }}</div>
            </div>
          </div>

          <!-- 答复消息（客服回复，右侧） -->
          <div v-if="message.replyFeedbackStatus === '0'" class="message-right">
            <div class="message-content">
              <div class="message-header">
                <span class="message-time">{{ formatDateTime(message.createTime) }}</span>
              </div>
              <div class="message-text">{{ message.content }}</div>
            </div>
            <div class="avatar">{{ getAvatarText(message.realName, '0') }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部输入框（仅在答复模式下显示） -->
    <div v-if="showReplyInput" class="chat-input-area">
      <div class="platform-footer-main">
        <div class="platform-footer-send"
             @mouseenter="inputHoving = true"
             @mouseleave="inputHoving = false">
          <div class="platform-footer-send__content">
            <div class="platform-footer-send__textarea">
              <div class="platform-footer-send-div">
                <div>
                  <el-input ref="inputContent" placeholder="请输入您的答复内容..." @focus="handleFocus"
                            @blur="visible = false"
                            @input="handleInput"
                            v-model="newMessage" type="textarea" :autosize="{maxRows: 3}"
                            @keydown.enter.native="enterClick($event)"
                            :class="{'stateInput': inputLength}"
                            class="inpTextarea"
                            :maxlength="200" clearable></el-input>
                </div>
                <div class="platform-footer-send__number"
                     :class="textNumber === 0 && 'hidden'" v-show="inputLength"><span
                    :style="{'color': textNumber === 200 ?'red':''}">{{ textNumber }}</span> /200
                </div>
              </div>
            </div>
            <div class="platform-footer-send__button send__button_all" @click="handleSendMessage"
                 :class="{'disabled':!newMessage.trim(),'butSendTop':inputLength}">
              <i class="iconfont icon-ic-send-o"></i>
              <span>答复</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { _getConversationRecordList } from "@/api/chat";
import { _replyFeedback } from "@/api/feedbackReply-api";

export default {
  name: "replyDetailDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    feedbackData: {
      type: Object,
      default: () => ({})
    },
    dialogType: {
      type: String,
      default: 'history' // 'history' 或 'reply'
    }
  },
  data() {
    return {
      conversationList: [],
      newMessage: '',
      chatLoading: true,
      visible: false, // 发送输入框是否是focus状态
      inputHoving: false, // 发送输入框是否是焦点状态
      inputLength: false
    }
  },
  computed: {
    showReplyInput() {
      return this.dialogType === 'reply';
    },
    // 计算发送输入框输入的文本字数
    textNumber() {
      return this.newMessage.length;
    }
  },
  created() {
    this.getConversationData();
  },
  methods: {
    getConversationData() {
      this.chatLoading = true;
      const params = {
        feedbackId: this.feedbackData.id
      };
      _getConversationRecordList(params).then(res => {
        if (res.data.success) {
          this.conversationList = res.data.result || [];
        }
      }).catch(err => {
        console.error('获取对话记录失败:', err);
        this.$message.error('获取对话记录失败');
      }).finally(() => {
        this.chatLoading = false;
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      });
    },
    getAvatarText(realName, type) {
      if (!realName)  {
        if (type === '0') return '我';
        if (type === '1') return '用户';
      }
      // 取最后两个字符作为头像
      return realName.length >= 2 ? realName.slice(-2) : realName;
    },
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      // 格式化时间显示为 2025/07/08 12:00:09 格式
      const date = new Date(dateTimeStr);

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
    },
    scrollToBottom() {
      const container = this.$refs.chatContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    handleSendMessage() {
      if (!this.newMessage.trim()) {
        return;
      }

      const content = this.newMessage.trim();
      const params = {
        feedbackId: this.feedbackData.id,
        content: content
      };

      // 调用后端API提交答复
      _replyFeedback(params).then(res => {
        if (res.data.success) {
          this.$message.success('答复提交成功');
          // 清空输入框
          this.newMessage = '';
          // 通知父组件关闭dialog并刷新列表
          this.$emit('submit-success');
        } else {
          this.$message.error(res.data.errorMsg || '答复提交失败');
        }
      }).catch(err => {
        console.error('答复提交失败:', err);
        this.$message.error('答复提交失败');
      });
    },
    handleClose() {
      // 清空输入框
      this.newMessage = '';
      this.$emit('close');
    },
    handleInput() {
      this.$nextTick(() => {
        const textarea = this.$refs.inputContent.$el.querySelector('textarea');
        const content = textarea.value;
        const lines = content.split('\n');
        let lineCount = lines.length;

        // 对于每一行，检查是否超过了文本域的宽度
        for (let line of lines) {
          const lineWidth = this.getTextWidth(line, textarea);
          const textareaWidth = textarea.clientWidth;
          if (lineWidth > textareaWidth) {
            // 计算这一行可以容纳多少个字符
            const charWidth = lineWidth / line.length;
            const maxCharsPerLine = Math.floor(textareaWidth / charWidth);
            lineCount += Math.ceil(line.length / maxCharsPerLine) - 1;
          }
        }
        this.inputLength = lineCount >= 2;
      });
    },
    getTextWidth(text, element) {
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      context.font = window.getComputedStyle(element).font;
      return context.measureText(text).width;
    },
    // 发送输入框获取焦点
    handleFocus() {
      this.visible = true;
    },
    // 回车事件
    enterClick(event) {
      if (!event.shiftKey && event.keyCode === 13) {
        event.cancelBubble = true;
        event.stopPropagation();
        event.preventDefault();
        this.handleSendMessage();
      }
    }
  }
}
</script>

<style lang="scss">
.reply-detail-dialog {
  .header-qa-content {
    padding: 10px 20px;
    background: #f9f9f9;
    height: 70px;
    overflow-y: auto;

    .qa-item {
      display: flex;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .qa-label {
        color: #999;
        font-size: 13px;
      }

      .qa-content {
        color: #333;
        font-size: 13px;
      }
    }
  }

  .chat-container {
    height: 400px;
    overflow-y: auto;
    padding: 16px;
    background: #ffffff;

    .chat-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      color: #666;

      i {
        font-size: 24px;
        margin-bottom: 8px;
      }

      span {
        font-size: 14px;
      }
    }

    .chat-messages {
      .message-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .message-left {
          display: flex;
          align-items: flex-start;

          .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: transparent;
            border: 1px solid #7E9FFE;
            color: #7E9FFE;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            margin-right: 12px;
            flex-shrink: 0;
          }

          .message-content {
            flex: 1;
            max-width: calc(100% - 60px);

            .message-header {
              display: flex;
              align-items: center;
              margin-bottom: 4px;

              .sender-name {
                font-size: 12px;
                color: #666;
                margin-right: 8px;
              }

              .message-time {
                font-size: 12px;
                color: #999;
              }
            }

            .message-text {
              background: transparent;
              border: 1px solid #e0e0e0;
              padding: 10px 12px;
              border-radius: 0 12px 12px 12px;
              color: #333;
              line-height: 1.4;
              word-break: break-word;
              display: inline-block;
              max-width: 70%;
            }
          }
        }

        .message-right {
          display: flex;
          align-items: flex-start;
          justify-content: flex-end;

          .message-content {
            flex: 1;
            max-width: calc(100% - 60px);
            text-align: right;

            .message-header {
              display: flex;
              align-items: center;
              justify-content: flex-end;
              margin-bottom: 4px;

              .message-time {
                font-size: 12px;
                color: #999;
                margin-right: 8px;
              }

              .sender-name {
                font-size: 12px;
                color: #666;
              }
            }

            .message-text {
              background: transparent;
              border: 1px solid #e0e0e0;
              color: #333;
              padding: 10px 12px;
              border-radius: 12px 0 12px 12px;
              line-height: 1.4;
              word-break: break-word;
              text-align: left;
              display: inline-block;
              max-width: 70%;
            }
          }

          .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #7A9BFE;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            margin-left: 12px;
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .chat-input-area {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;

    .platform-footer-main {
      width: 80%;
      z-index: 99;
      position: relative;
      align-items: center;
      min-height: 52px;
    }

    .platform-footer-send {
      background: rgba(255, 255, 255, 0.95);
      box-shadow: 0 1px 2px rgba(65, 65, 65, 0.3);
      border-radius: 26px;
      padding: 12px 16px;
      overflow: hidden;
      flex: 1;
      transition: all 0.3s;

      .platform-footer-send__textarea {
        flex: 1;
        margin-right: 8px;
      }

      &.focus {
        .platform-footer-send__number {
          display: block;
        }
      }

      &.focus {
        min-height: 98px !important;
      }

      &.empty {
        min-height: 54px;
        height: 54px;
      }

      .platform-footer-send__content {
        display: flex;
      }

      .platform-footer-send__number {
        color: #666666;
        font-size: 14px;
        line-height: 22px;
        margin-top: 8px;
      }

      textarea {
        border: none;
        outline: none;
        flex: 1;
        margin-right: 16px;
        color: #111;
        height: 31px !important;
        resize: none;
        padding-left: 0;
        padding-right: 0;
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        overflow-y: scroll;
      }

      textarea::-webkit-input-placeholder {
        color: #999999;
        font-size: 14px;
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
      }

      textarea::-moz-placeholder {
        color: #999999;
        font-size: 14px;
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
      }

      textarea:-moz-placeholder {
        color: #999999;
        font-size: 14px;
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
      }

      textarea:-ms-input-placeholder {
        color: #999999;
        font-size: 14px;
        font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
      }

      .platform-footer-send__button {
        border-radius: 23px;
        height: 38px;
        line-height: 38px;
        width: 85px;
        text-align: center;
        color: #FFFFFF;
        font-size: 14px;
        cursor: pointer;
        margin-top: -4px;
        background-color: rgba(207, 26, 28, 1);

        &.disabled {
          background: #fae1e1;
          cursor: default;

          .iconfont {
            cursor: default;
          }
        }

        .iconfont {
          color: #fff;
          margin-right: 6px;
          font-size: 14px;
        }
      }
    }
  }
}

.stateInput .el-textarea__inner {
  min-height: 66px !important;
  height: 66px !important;
  padding-right: 105px;
}

.hidden {
  visibility: hidden;
}

.butSendTop {
  margin-top: -8px !important;
}

.inpTextarea {
  .el-textarea__inner {
    border: none !important;
    outline: none !important;
    background: transparent !important;
    padding: 0 !important;
    resize: none !important;
    box-shadow: none !important;
  }
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
